import React, { useState, useEffect, useRef } from 'react';
import { useWorkspace } from './workspaceContext';

/**
 * WorkspaceDiagnostics component for debugging workspace issues
 * This component displays detailed diagnostic information about the workspace context
 */
const WorkspaceDiagnostics: React.FC = () => {
  const { getWorkspaceDiagnostics, clearWorkspaceCache, fetchWorkspaces } = useWorkspace();
  const [diagnostics, setDiagnostics] = useState(getWorkspaceDiagnostics());
  const [expanded, setExpanded] = useState(false);
  const [refreshCount, setRefreshCount] = useState(0);
  const [logs, setLogs] = useState<string[]>([]);
  const [showLogs, setShowLogs] = useState(false);
  const logRef = useRef<HTMLDivElement>(null);
  const originalConsoleLog = useRef<typeof console.log | null>(null);
  const originalConsoleError = useRef<typeof console.error | null>(null);

  // Update diagnostics with configurable interval to reduce CPU usage
  useEffect(() => {
    // Get update interval from environment variable or default to 5 seconds
    const updateInterval = parseInt(process.env.REACT_APP_DIAGNOSTICS_UPDATE_INTERVAL || '5000');

    const interval = setInterval(() => {
      setDiagnostics(getWorkspaceDiagnostics());
    }, updateInterval);

    return () => clearInterval(interval);
  }, [getWorkspaceDiagnostics]);

  // Capture console logs related to workspaces
  useEffect(() => {
    if (!originalConsoleLog.current) {
      originalConsoleLog.current = console.log;
    }

    if (!originalConsoleError.current) {
      originalConsoleError.current = console.error;
    }

    console.log = (...args: any[]) => {
      originalConsoleLog.current?.(...args);

      // Only capture workspace-related logs
      const logString = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ');

      if (logString.includes('workspace') || logString.includes('Workspace') ||
          logString.includes('WORKSPACE') || logString.includes('/workspaces')) {
        setLogs(prev => [...prev.slice(-99), `[LOG] ${new Date().toISOString()}: ${logString}`]);
      }
    };

    console.error = (...args: any[]) => {
      originalConsoleError.current?.(...args);

      // Only capture workspace-related errors
      const logString = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ');

      if (logString.includes('workspace') || logString.includes('Workspace') ||
          logString.includes('WORKSPACE') || logString.includes('/workspaces')) {
        setLogs(prev => [...prev.slice(-99), `[ERROR] ${new Date().toISOString()}: ${logString}`]);
      }
    };

    return () => {
      if (originalConsoleLog.current) {
        console.log = originalConsoleLog.current;
      }

      if (originalConsoleError.current) {
        console.error = originalConsoleError.current;
      }
    };
  }, []);

  // Scroll to bottom of logs when new logs are added
  useEffect(() => {
    if (logRef.current && showLogs) {
      logRef.current.scrollTop = logRef.current.scrollHeight;
    }
  }, [logs, showLogs]);

  // Force refresh diagnostics when refresh button is clicked
  const handleRefresh = () => {
    setDiagnostics(getWorkspaceDiagnostics());
    setRefreshCount(prev => prev + 1);
  };

  // Force fetch workspaces
  const handleForceFetch = async () => {
    try {
      await fetchWorkspaces(true);
      handleRefresh();
    } catch (err) {
      console.error('Error forcing workspace fetch:', err);
    }
  };

  // Clear workspace cache
  const handleClearCache = async () => {
    try {
      await clearWorkspaceCache();
      handleRefresh();
    } catch (err) {
      console.error('Error clearing workspace cache:', err);
    }
  };

  // Toggle expanded state
  const toggleExpanded = () => {
    setExpanded(!expanded);
    // Hide logs when collapsing
    if (expanded) {
      setShowLogs(false);
    }
  };

  // Toggle logs visibility
  const toggleLogs = () => {
    setShowLogs(!showLogs);
  };

  // Clear logs
  const clearLogs = () => {
    setLogs([]);
  };

  // Determine status color
  const getStatusColor = () => {
    if (diagnostics.error) return 'bg-red-500';
    if (diagnostics.circuitBreakerOpen) return 'bg-orange-500';
    if (diagnostics.isLoading || diagnostics.isLoadingRef) return 'bg-blue-500';
    if (diagnostics.workspaceCount === 0) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Collapsed view */}
      {!expanded && (
        <button
          onClick={toggleExpanded}
          className={`${getStatusColor()} text-white p-2 rounded-full shadow-lg hover:opacity-90 transition-opacity`}
          title="Workspace Diagnostics"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      )}

      {/* Expanded view */}
      {expanded && (
        <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 ${showLogs ? 'w-[800px]' : 'w-80'} border border-gray-200 dark:border-gray-700 transition-all duration-300`}>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Workspace Diagnostics</h3>
            <div className="flex space-x-2">
              <button
                onClick={toggleLogs}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                title={showLogs ? "Hide Logs" : "Show Logs"}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" />
                  <path d="M8 11a1 1 0 100-2H6a1 1 0 000 2h2zm2-3a1 1 0 100-2H6a1 1 0 100 2h4zm-2 5a1 1 0 100-2H6a1 1 0 000 2h2z" />
                </svg>
              </button>
              <button
                onClick={toggleExpanded}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-4">
            <div className={`${showLogs ? 'w-full md:w-1/2' : 'w-full'}`}>
              <div className="space-y-2 text-sm text-gray-700 dark:text-gray-300 mb-4 max-h-80 overflow-y-auto">
                <div className="flex justify-between">
                  <span>Workspaces:</span>
                  <span className="font-mono">{diagnostics.workspaceCount}</span>
                </div>
                <div className="flex justify-between">
                  <span>Current Workspace ID:</span>
                  <span className="font-mono">{diagnostics.currentWorkspaceId || 'none'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Loading:</span>
                  <span className={`font-mono ${diagnostics.isLoading || diagnostics.isLoadingRef ? 'text-blue-500' : ''}`}>
                    {diagnostics.isLoading ? 'true' : 'false'} / {diagnostics.isLoadingRef ? 'true' : 'false'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Error:</span>
                  <span className={`font-mono ${diagnostics.error ? 'text-red-500' : ''}`}>
                    {diagnostics.error ? 'yes' : 'none'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Auth Error:</span>
                  <span className={`font-mono ${diagnostics.authErrorOccurred ? 'text-red-500' : ''}`}>
                    {diagnostics.authErrorOccurred ? 'yes' : 'no'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Last Fetch:</span>
                  <span className="font-mono">{diagnostics.timeSinceLastFetch}</span>
                </div>
                <div className="flex justify-between">
                  <span>Consecutive Errors:</span>
                  <span className={`font-mono ${diagnostics.consecutiveErrors > 0 ? 'text-red-500' : ''}`}>
                    {diagnostics.consecutiveErrors}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Circuit Breaker:</span>
                  <span className={`font-mono ${diagnostics.circuitBreakerOpen ? 'text-red-500' : ''}`}>
                    {diagnostics.circuitBreakerOpen ? 'open' : 'closed'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>LocalStorage ID:</span>
                  <span className="font-mono">{diagnostics.localStorageWorkspaceId || 'none'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Initial Set:</span>
                  <span className="font-mono">{diagnostics.initialWorkspaceSet ? 'yes' : 'no'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Refresh Count:</span>
                  <span className="font-mono">{refreshCount}</span>
                </div>
                <div className="flex justify-between">
                  <span>Captured Logs:</span>
                  <span className="font-mono">{logs.length}</span>
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={handleRefresh}
                  className="flex-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 py-1 px-2 rounded text-xs"
                >
                  Refresh
                </button>
                <button
                  onClick={handleForceFetch}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded text-xs"
                >
                  Force Fetch
                </button>
                <button
                  onClick={handleClearCache}
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded text-xs"
                >
                  Clear Cache
                </button>
              </div>
            </div>

            {/* Logs panel */}
            {showLogs && (
              <div className="w-full md:w-1/2">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">Workspace Logs</h4>
                  <button
                    onClick={clearLogs}
                    className="text-xs bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 py-1 px-2 rounded"
                  >
                    Clear Logs
                  </button>
                </div>
                <div
                  ref={logRef}
                  className="h-80 overflow-y-auto font-mono text-xs bg-gray-800 text-gray-200 p-2 rounded"
                >
                  {logs.length > 0 ? (
                    logs.map((log, index) => (
                      <div
                        key={index}
                        className={`whitespace-pre-wrap mb-1 ${log.includes('[ERROR]') ? 'text-red-400' : ''}`}
                      >
                        {log}
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500">No workspace logs captured yet</p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkspaceDiagnostics;
