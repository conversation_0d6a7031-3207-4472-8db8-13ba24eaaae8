#!/bin/bash

# Start Expense Logger in development mode with optimized settings for low CPU usage
echo "Starting Expense Logger in development mode with CPU optimizations..."

# Check if docker-compose.override.yml exists
if [ ! -f "docker-compose.override.yml" ]; then
    echo "Error: docker-compose.override.yml not found!"
    echo "This file is required for optimized development settings."
    exit 1
fi

# Start the services
echo "Starting services with optimized settings..."
docker-compose up --build

echo "Services started with the following optimizations:"
echo "- Frontend: Fast refresh disabled, file polling disabled"
echo "- Backend: Reload delay increased to 2 seconds"
echo "- Workspace diagnostics: Update interval set to 5 seconds"
echo "- Periodic workspace refresh: Disabled in development"
