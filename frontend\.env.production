# Production environment configuration

# API URL - placeholder that will be replaced by Docker at runtime
REACT_APP_API_URL=%REACT_APP_API_URL%

# Environment name
REACT_APP_ENV=production

# Feature flags
REACT_APP_ENABLE_OCR_TEMPLATES=true
REACT_APP_ENABLE_WISHLIST=true
REACT_APP_ENABLE_DARK_MODE=true

# Authentication settings
REACT_APP_AUTH_STORAGE_KEY=expense_logger_auth
REACT_APP_AUTH_TOKEN_EXPIRY=86400 # 24 hours in seconds
REACT_APP_AUTH_REFRESH_THRESHOLD=3600 # 1 hour in seconds

# Analytics and monitoring (enabled in production)
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_SENTRY_DSN=%REACT_APP_SENTRY_DSN%

# Development tools (always disabled in production)
REACT_APP_ENABLE_MOCK_API=false
REACT_APP_SHOW_DEBUG_INFO=false

# Upload limits
REACT_APP_MAX_UPLOAD_SIZE=25165824 # 24 MB in bytes
REACT_APP_ALLOWED_FILE_TYPES=.pdf,.png,.jpg,.jpeg

# Application defaults
REACT_APP_DEFAULT_CURRENCY=USD
REACT_APP_DEFAULT_DATE_FORMAT=MM/DD/YYYY
REACT_APP_DEFAULT_LANGUAGE=en-US

# Performance settings (enabled in production for data freshness)
REACT_APP_ENABLE_PERIODIC_REFRESH=true
REACT_APP_DIAGNOSTICS_UPDATE_INTERVAL=10000

# Build optimization
GENERATE_SOURCEMAP=false