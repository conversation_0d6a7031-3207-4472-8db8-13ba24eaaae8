#!/usr/bin/env python3

import sys
import os
sys.path.append('/app')

from core.database import SessionLocal
from features.invoices.models import Invoice
from features.auth.models import User
from features.workspace.models import Workspace
from sqlalchemy import extract, func
import calendar

def test_dashboard_data():
    db = SessionLocal()
    try:
        print("=== Testing Dashboard Data ===")
        
        # Test basic invoice data
        invoices = db.query(Invoice).filter(
            Invoice.is_deleted == False,
            Invoice.workspace_id == 1
        ).all()
        
        print(f"Found {len(invoices)} invoices in workspace 1")
        for inv in invoices:
            print(f"  Invoice {inv.invoice_id}: Date={inv.purchase_date}, Total=${inv.grand_total}, Status={inv.status}")
        
        print("\n=== Testing Monthly Data Query ===")
        # Test monthly spending query
        monthly_query = db.query(
            extract('month', Invoice.purchase_date).label('month'),
            func.sum(Invoice.grand_total).label('amount')
        ).filter(
            Invoice.is_deleted == False,
            Invoice.workspace_id == 1,
            extract('year', Invoice.purchase_date) == 2025
        ).group_by(extract('month', Invoice.purchase_date))
        
        monthly_data = monthly_query.all()
        print(f"Monthly data points: {len(monthly_data)}")
        for month_num, amount in monthly_data:
            month_name = calendar.month_name[int(month_num)]
            print(f"  {month_name}: ${amount}")
        
        print("\n=== Testing Category Data ===")
        # Test category data
        for invoice in invoices:
            categories = invoice.categories or []
            print(f"  Invoice {invoice.invoice_id} categories: {[cat.category_name if hasattr(cat, 'category_name') else str(cat) for cat in categories]}")
        
        print("\n=== Testing Status Data ===")
        # Test status distribution
        status_query = db.query(
            Invoice.status,
            func.count(Invoice.invoice_id).label('count')
        ).filter(
            Invoice.is_deleted == False,
            Invoice.workspace_id == 1
        ).group_by(Invoice.status)
        
        status_data = status_query.all()
        print(f"Status distribution: {len(status_data)} statuses")
        for status, count in status_data:
            print(f"  {status}: {count}")
        
        print("\n=== Testing Payment Method Data ===")
        # Test payment method distribution
        payment_query = db.query(
            Invoice.payment_method,
            func.sum(Invoice.grand_total).label('amount')
        ).filter(
            Invoice.is_deleted == False,
            Invoice.workspace_id == 1
        ).group_by(Invoice.payment_method)
        
        payment_data = payment_query.all()
        print(f"Payment method distribution: {len(payment_data)} methods")
        for method, amount in payment_data:
            print(f"  {method}: ${amount}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_dashboard_data()
