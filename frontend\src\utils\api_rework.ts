// src/utils/api_rework.ts
// Improved API utility file for making API requests with better error handling

import apiRequestManager from './apiRequestManager';

// Define API_URL with fallback
export const API_URL = process.env.REACT_APP_API_URL || "http://localhost:8000";

// Request queue implementation to limit concurrent requests
class RequestQueue {
  private queue: Array<() => Promise<any>> = [];
  private running = 0;
  private maxConcurrent = 3; // Maximum number of concurrent requests

  constructor(maxConcurrent = 3) {
    this.maxConcurrent = maxConcurrent;
  }

  public enqueue<T>(request: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      // Add the request to the queue
      this.queue.push(() =>
        request()
          .then(resolve)
          .catch(reject)
          .finally(() => {
            this.running--;
            this.processNext();
          })
      );

      // Process the queue
      this.processNext();
    });
  }

  private processNext(): void {
    // If we're already at max concurrent requests, do nothing
    if (this.running >= this.maxConcurrent) {
      return;
    }

    // If there are no more requests in the queue, do nothing
    const nextRequest = this.queue.shift();
    if (!nextRequest) {
      return;
    }

    // Execute the next request
    this.running++;
    nextRequest();
  }
}

// Create a global request queue
const requestQueue = new RequestQueue(3);

/**
 * Generic function to handle error responses
 */
export const handleApiError = async (response: Response, endpoint: string): Promise<never> => {
  try {
    // Handle authentication errors specifically
    if (response.status === 401) {
      // Only clear tokens for authentication-related endpoints or profile endpoints
      // Don't clear tokens for other endpoints that might fail due to ORM issues
      const isAuthEndpoint = endpoint.includes('/auth/') || endpoint.includes('/temp-auth/profile');
      const isLoginEndpoint = endpoint.includes('/auth/login') || endpoint.includes('/auth/refresh') || endpoint.includes('/temp-auth/login');

      if (isAuthEndpoint && !isLoginEndpoint) {
        // Clear token from localStorage to force re-login
        localStorage.removeItem('token');
        localStorage.removeItem('userData');
        localStorage.removeItem('currentWorkspace');

        // Dispatch an authentication error event that can be caught by other components
        const authErrorEvent = new CustomEvent('auth-error', {
          detail: {
            status: response.status,
            message: 'Session expired or unauthorized access',
            endpoint
          }
        });
        window.dispatchEvent(authErrorEvent);

        console.error(`Authentication error detected in API call to ${endpoint}`);
      } else {
        // For non-auth endpoints, just log the error but don't logout
        console.warn(`401 error on ${endpoint} - likely due to ORM issues, not logging out`);
      }

      // Try to parse the error response for more details
      try {
        const errorData = await response.json();
        const errorMessage = errorData.detail || 'Authentication error. Please log in again.';
        throw new Error(errorMessage);
      } catch (parseError) {
        throw new Error('Authentication error. Please log in again.');
      }
    }

    // Handle rate limiting errors (429 Too Many Requests)
    if (response.status === 429) {
      console.warn(`Rate limit exceeded for ${endpoint}`);

      // Dispatch a rate limit event that can be caught by components
      const rateLimitEvent = new CustomEvent('rate-limit-error', {
        detail: {
          status: response.status,
          message: 'Rate limit exceeded. Please try again later.',
          endpoint
        }
      });
      window.dispatchEvent(rateLimitEvent);

      // Try to parse the error response for more details
      try {
        const errorData = await response.json();
        const errorMessage = errorData.detail || 'Rate limit exceeded. Please try again later.';
        throw new Error(errorMessage);
      } catch (parseError) {
        throw new Error('Rate limit exceeded. Please try again later.');
      }
    }

    // Try to parse the error response
    try {
      const errorData = await response.json();
      const errorMessage = errorData.detail || errorData.message || `Error ${response.status}: ${response.statusText}`;
      console.error(`API error (${endpoint}):`, errorMessage, errorData);
      throw new Error(errorMessage);
    } catch (parseError) {
      // If we can't parse the JSON, just use the status
      console.error(`API error (${endpoint}, ${response.status}): ${response.statusText}`);
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    } else {
      throw new Error(`HTTP error ${response.status}`);
    }
  }
};

/**
 * Check if the API server is available
 */
export const checkApiAvailability = async (): Promise<boolean> => {
  try {
    // Make a simple OPTIONS request to check if the server is up
    const response = await fetch(`${API_URL}/health-check`, {
      method: 'OPTIONS',
      headers: {
        'Accept': 'application/json',
      },
      // Don't include credentials for the health check
      mode: 'cors',
    });

    if (response.ok) {
      console.log('API server is available');
      return true;
    }

    // If OPTIONS fails, try a GET request as fallback
    try {
      const getResponse = await fetch(`${API_URL}/health-check`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        mode: 'cors',
      });

      console.log('API server availability (GET fallback):', getResponse.ok);
      return getResponse.ok;
    } catch (getError) {
      console.warn('API server GET availability check failed:', getError);
      return false;
    }
  } catch (error) {
    console.warn('API server OPTIONS availability check failed:', error);
    return false;
  }
};

/**
 * Generic GET request with better error handling, request queuing, and deduplication
 */
export const apiGet = async <T>(endpoint: string, token?: string | null, retries = 2, forceRefresh = false): Promise<T> => {
  // Create a unique key for this request
  const requestKey = `GET:${endpoint}`;

  // Special handling for workspace endpoint - use longer throttling
  const minInterval = endpoint.startsWith('/workspaces') ? 60000 : 5000; // 60 seconds for workspace endpoint

  // Use the request manager to deduplicate requests
  return apiRequestManager.executeRequest<T>(
    requestKey,
    () => requestQueue.enqueue(async () => {
        const headers: HeadersInit = {
          'Accept': 'application/json',
        };

        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        try {
          console.log(`Making GET request to ${endpoint}`);

          // First try with credentials: 'include'
          try {
            const response = await fetch(`${API_URL}${endpoint}`, {
              method: 'GET',
              headers,
              credentials: 'include', // Include cookies for authentication
              mode: 'cors',
            });

            if (!response.ok) {
              await handleApiError(response, endpoint);
            }

            return response.json();
          } catch (corsError) {
            // If we get a CORS error, try again without credentials
            if (corsError instanceof Error &&
                (corsError.message.includes('CORS') ||
                corsError.message.includes('blocked by CORS policy'))) {
              console.warn(`CORS error with credentials, retrying without credentials: ${endpoint}`);

              const fallbackResponse = await fetch(`${API_URL}${endpoint}`, {
                method: 'GET',
                headers,
                mode: 'cors',
                // No credentials
              });

              if (!fallbackResponse.ok) {
                await handleApiError(fallbackResponse, endpoint);
              }

              return fallbackResponse.json();
            } else {
              // If it's not a CORS error, rethrow
              throw corsError;
            }
          }
        } catch (error) {
          // Handle network errors (like CORS, server down, etc.)
          console.error(`Network error fetching ${endpoint}:`, error);

          // Implement retry logic with exponential backoff
          if (retries > 0 && error instanceof Error &&
              (error.message.includes('INSUFFICIENT_RESOURCES') ||
               error.message.includes('Network request failed') ||
               error.message.includes('Failed to fetch') ||
               error.message.includes('CORS') ||
               error.message.includes('blocked by CORS policy') ||
               error.message.includes('NetworkError'))) {
            console.log(`Retrying GET request to ${endpoint}, ${retries} retries left`);
            // Wait before retrying (exponential backoff)
            const delay = 1000 * (3 - retries);
            await new Promise(resolve => setTimeout(resolve, delay));
            return apiGet(endpoint, token, retries - 1);
          }

          if (error instanceof Error) {
            throw error;
          } else {
            throw new Error('Network request failed');
          }
        }
      }),
      minInterval,
      forceRefresh
    );
};

/**
 * Generic POST request with JSON body and request queuing
 */
export const apiPost = async <T>(endpoint: string, data: any, token?: string | null, retries = 2): Promise<T> => {
  return requestQueue.enqueue(async () => {
    const headers: HeadersInit = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      console.log(`Making POST request to ${endpoint}`);
      const response = await fetch(`${API_URL}${endpoint}`, {
        method: 'POST',
        headers,
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        await handleApiError(response, endpoint);
      }

      return response.json();
    } catch (error) {
      // Handle network errors
      console.error(`Network error posting to ${endpoint}:`, error);

      // Implement retry logic with exponential backoff
      if (retries > 0 && error instanceof Error &&
          (error.message.includes('INSUFFICIENT_RESOURCES') ||
           error.message.includes('Network request failed') ||
           error.message.includes('Failed to fetch'))) {
        console.log(`Retrying POST request to ${endpoint}, ${retries} retries left`);
        // Wait before retrying (exponential backoff)
        const delay = 1000 * (3 - retries);
        await new Promise(resolve => setTimeout(resolve, delay));
        return apiPost(endpoint, data, token, retries - 1);
      }

      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error('Network request failed');
      }
    }
  });
};

/**
 * Generic PUT request with JSON body and request queuing
 */
export const apiPut = async <T>(endpoint: string, data: any, token?: string | null, retries = 2): Promise<T> => {
  return requestQueue.enqueue(async () => {
    const headers: HeadersInit = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      console.log(`Making PUT request to ${endpoint}`);
      const response = await fetch(`${API_URL}${endpoint}`, {
        method: 'PUT',
        headers,
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        await handleApiError(response, endpoint);
      }

      return response.json();
    } catch (error) {
      // Handle network errors
      console.error(`Network error putting to ${endpoint}:`, error);

      // Implement retry logic with exponential backoff
      if (retries > 0 && error instanceof Error &&
          (error.message.includes('INSUFFICIENT_RESOURCES') ||
           error.message.includes('Network request failed') ||
           error.message.includes('Failed to fetch'))) {
        console.log(`Retrying PUT request to ${endpoint}, ${retries} retries left`);
        // Wait before retrying (exponential backoff)
        const delay = 1000 * (3 - retries);
        await new Promise(resolve => setTimeout(resolve, delay));
        return apiPut(endpoint, data, token, retries - 1);
      }

      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error('Network request failed');
      }
    }
  });
};

/**
 * Generic DELETE request with request queuing
 */
export const apiDelete = async <T>(endpoint: string, token?: string | null, retries = 2): Promise<T> => {
  return requestQueue.enqueue(async () => {
    const headers: HeadersInit = {};

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      console.log(`Making DELETE request to ${endpoint}`);
      const response = await fetch(`${API_URL}${endpoint}`, {
        method: 'DELETE',
        headers,
        credentials: 'include' // Include cookies for authentication
      });

      if (!response.ok) {
        await handleApiError(response, endpoint);
      }

      return response.json();
    } catch (error) {
      // Handle network errors
      console.error(`Network error deleting ${endpoint}:`, error);

      // Implement retry logic with exponential backoff
      if (retries > 0 && error instanceof Error &&
          (error.message.includes('INSUFFICIENT_RESOURCES') ||
           error.message.includes('Network request failed') ||
           error.message.includes('Failed to fetch'))) {
        console.log(`Retrying DELETE request to ${endpoint}, ${retries} retries left`);
        // Wait before retrying (exponential backoff)
        const delay = 1000 * (3 - retries);
        await new Promise(resolve => setTimeout(resolve, delay));
        return apiDelete(endpoint, token, retries - 1);
      }

      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error('Network request failed');
      }
    }
  });
};

/**
 * Upload file to the API with request queuing
 */
export const apiUploadFile = async <T>(
  endpoint: string,
  file: File,
  formData?: Record<string, string>,
  token?: string | null,
  retries = 2
): Promise<T> => {
  return requestQueue.enqueue(async () => {
    const data = new FormData();
    data.append('file', file);

    // Add any additional form data
    if (formData) {
      Object.entries(formData).forEach(([key, value]) => {
        data.append(key, value);
      });
    }

    const headers: HeadersInit = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      console.log(`Making file upload request to ${endpoint}`);
      const response = await fetch(`${API_URL}${endpoint}`, {
        method: 'POST',
        headers,
        credentials: 'include', // Include cookies for authentication
        body: data
      });

      if (!response.ok) {
        await handleApiError(response, endpoint);
      }

      return response.json();
    } catch (error) {
      // Handle network errors
      console.error(`Network error uploading to ${endpoint}:`, error);

      // Implement retry logic with exponential backoff
      if (retries > 0 && error instanceof Error &&
          (error.message.includes('INSUFFICIENT_RESOURCES') ||
           error.message.includes('Network request failed') ||
           error.message.includes('Failed to fetch'))) {
        console.log(`Retrying file upload request to ${endpoint}, ${retries} retries left`);
        // Wait before retrying (exponential backoff)
        const delay = 1000 * (3 - retries);
        await new Promise(resolve => setTimeout(resolve, delay));
        return apiUploadFile(endpoint, file, formData, token, retries - 1);
      }

      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error('Network request failed');
      }
    }
  });
};

export default {
  API_URL,
  apiGet,
  apiPost,
  apiPut,
  apiDelete,
  apiUploadFile,
  handleApiError,
  checkApiAvailability
};
