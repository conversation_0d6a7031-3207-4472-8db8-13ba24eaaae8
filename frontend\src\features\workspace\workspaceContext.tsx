// src/features/workspace/workspaceContext.tsx
import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { apiGet, apiPost, apiPut, apiDelete } from '../../utils/api_rework';
import { useAuth } from '../auth/authContext';

// Define workspace types
export interface Workspace {
  workspace_id: number;
  name: string;
  description?: string;
  created_by: number;
  created_at: string;
  updated_at: string;
  invoice_count?: number;
  expense_count?: number;
}

export interface WorkspaceUser {
  workspace_user_id: number;
  workspace_id: number;
  user_id: number;
  role: string;
  created_at: string;
  updated_at: string;
}

// Define the diagnostic information type
interface WorkspaceDiagnostics {
  workspaceCount: number;
  currentWorkspaceId?: number;
  isLoading: boolean;
  isLoadingRef: boolean;
  error: string | null;
  authErrorOccurred: boolean;
  lastFetchTime: string | null;
  timeSinceLastFetch: string;
  consecutiveErrors: number;
  circuitBreakerOpen: boolean;
  localStorageWorkspaceId: string | null;
  initialWorkspaceSet: boolean;
}

interface WorkspaceContextType {
  workspaces: Workspace[];
  currentWorkspace: Workspace | null;
  isLoading: boolean;
  error: string | null;
  fetchWorkspaces: (force?: boolean) => Promise<void>;
  setCurrentWorkspace: (workspace: Workspace | null) => void;
  switchWorkspace: (workspace: Workspace) => void;
  createWorkspace: (name: string, description?: string) => Promise<Workspace>;
  updateWorkspace: (id: number, name: string, description?: string) => Promise<Workspace>;
  deleteWorkspace: (id: number) => Promise<void>;
  addUserToWorkspace: (workspaceId: number, userId: number, role: string) => Promise<WorkspaceUser>;
  removeUserFromWorkspace: (workspaceId: number, userId: number) => Promise<void>;
  updateUserRole: (workspaceId: number, userId: number, role: string) => Promise<WorkspaceUser>;
  getWorkspaceUsers: (workspaceId: number) => Promise<WorkspaceUser[]>;
  clearWorkspaceCache: () => Promise<void>;
  getWorkspaceDiagnostics: () => WorkspaceDiagnostics; // Add diagnostic function
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

export const WorkspaceProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [authErrorOccurred, setAuthErrorOccurred] = useState<boolean>(false);
  const { isAuthenticated, getToken } = useAuth();
  const navigate = useNavigate();
  const lastFetchTimeRef = useRef<number>(0);
  const initialWorkspaceSet = useRef<boolean>(false);
  const isLoadingRef = useRef<boolean>(false);
  const consecutiveErrorsRef = useRef<number>(0);
  const circuitBreakerOpenRef = useRef<boolean>(false);
  const circuitBreakerTimeoutRef = useRef<number | null>(null);

  // Define fetchWorkspaces function with useCallback to prevent recreation on every render
  const fetchWorkspaces = useCallback(async (force = false) => {
    // Skip if not authenticated or if auth error already occurred
    if (!isAuthenticated) {
      console.log('Not authenticated, skipping workspace fetch');
      return;
    }

    if (authErrorOccurred) {
      console.log('Authentication error already occurred, skipping workspace fetch');
      return;
    }

    // Check if circuit breaker is open (too many consecutive errors)
    if (circuitBreakerOpenRef.current && !force) {
      console.log('Circuit breaker is open, skipping workspace fetch');
      return;
    }

    // Throttle API calls - don't fetch more than once every 60 seconds unless forced
    const now = Date.now();
    if (!force && now - lastFetchTimeRef.current < 60000) {
      console.log(`Throttling workspace fetch - too soon since last fetch (${Math.round((now - lastFetchTimeRef.current) / 1000)}s elapsed, need 60s)`);
      return;
    }

    // Check if a fetch is already in progress
    if (isLoadingRef.current && !force) {
      console.log('Workspace fetch already in progress, skipping');
      return;
    }

    // If we have workspaces and this isn't a forced fetch, be more conservative with refetching
    if (!force && workspaces.length > 0) {
      // Only fetch every 5 minutes if we already have workspaces
      if (now - lastFetchTimeRef.current < 300000) {
        console.log('Already have workspaces, using more conservative throttling (5 minutes)');
        return;
      }
    }

    console.log(`Workspace fetch starting at ${new Date().toISOString()}`);
    lastFetchTimeRef.current = now;

    setIsLoading(true);
    isLoadingRef.current = true;
    setError(null);

    try {
      // Get token with verification
      const token = await getToken();

      // If token is null after verification, authentication failed
      if (!token) {
        console.log('Authentication failed, cannot fetch workspaces');
        setWorkspaces([]);
        setCurrentWorkspace(null);
        localStorage.removeItem('currentWorkspace');
        setError('Authentication error. Please log in again.');
        setAuthErrorOccurred(true);
        setIsLoading(false);
        return;
      }

      // Fetch workspaces with valid token
      try {
        console.log('Fetching workspaces with token');
        // Use the endpoint with trailing slash to avoid redirect
        // Pass force parameter to the API call to control deduplication
        const data = await apiGet<Workspace[]>('/workspaces/', token, 2, force);
        console.log('Workspaces fetched successfully:', data);

        // Reset consecutive errors counter on success
        if (consecutiveErrorsRef.current > 0) {
          console.log(`Resetting consecutive errors counter from ${consecutiveErrorsRef.current} to 0`);
          consecutiveErrorsRef.current = 0;
        }

        // Update workspaces state with fresh data
        setWorkspaces(data);

        // If no current workspace is set and we have workspaces
        if (!currentWorkspace && data.length > 0) {
          // Try to find the last selected workspace from localStorage
          try {
            const lastWorkspaceId = localStorage.getItem('lastWorkspaceId');
            if (lastWorkspaceId) {
              // Find the workspace with the saved ID
              const savedWorkspace = data.find(w => w.workspace_id.toString() === lastWorkspaceId);
              if (savedWorkspace) {
                console.log(`Found saved workspace: ${savedWorkspace.name} (ID: ${savedWorkspace.workspace_id})`);
                setCurrentWorkspace(savedWorkspace);
              } else {
                // If saved workspace not found, use the first one
                console.log(`Saved workspace ID ${lastWorkspaceId} not found, using first workspace`);
                setCurrentWorkspace(data[0]);
              }
            } else {
              // No saved workspace ID, use the first one
              console.log('No saved workspace ID, using first workspace');
              setCurrentWorkspace(data[0]);
            }
          } catch (err) {
            // If there's any error reading from localStorage, use the first workspace
            console.error('Error reading from localStorage:', err);
            setCurrentWorkspace(data[0]);
          }
        } else if (currentWorkspace) {
          // If we have a current workspace, update it with fresh data
          const updatedCurrentWorkspace = data.find(w => w.workspace_id === currentWorkspace.workspace_id);
          if (updatedCurrentWorkspace) {
            // Update the current workspace with fresh data
            setCurrentWorkspace(updatedCurrentWorkspace);
          } else if (data.length > 0) {
            // If current workspace is not found in the updated list but we have workspaces,
            // set the first one as current
            setCurrentWorkspace(data[0]);
          }
        }
      } catch (apiError) {
        console.error('API error fetching workspaces:', apiError);

        // Handle different types of errors
        if (apiError instanceof Error) {
          if (apiError.message.includes('Authentication') || apiError.message.includes('401')) {
            // Authentication error - clear token and redirect to login
            console.log('Authentication error detected, clearing token');
            localStorage.removeItem('token'); // Keep this for auth token only
            setWorkspaces([]);
            setCurrentWorkspace(null);
            setError('Authentication error. Please log in again.');
            setAuthErrorOccurred(true); // Set flag to prevent further fetch attempts
            // Don't redirect here to avoid interrupting the current operation
          } else if (apiError.message.includes('Failed to fetch') ||
                    apiError.message.includes('NetworkError') ||
                    apiError.message.includes('Network request failed') ||
                    apiError.message.includes('Connection refused')) {
            // Network error - backend might be down or restarting
            setError('Cannot connect to server. Please try again later.');
          } else {
            // Other API errors
            setError(apiError.message);
          }
        } else {
          setError('Unknown error connecting to server');
        }
      }
    } catch (err) {
      console.error('Error in workspace context:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');

      // Increment consecutive errors counter
      consecutiveErrorsRef.current += 1;
      console.log(`Workspace fetch error - consecutive errors: ${consecutiveErrorsRef.current}`);

      // If we've had too many consecutive errors, open the circuit breaker
      if (consecutiveErrorsRef.current >= 3) {
        console.log('Too many consecutive errors, opening circuit breaker');
        circuitBreakerOpenRef.current = true;

        // Reset the circuit breaker after 5 minutes
        if (circuitBreakerTimeoutRef.current) {
          clearTimeout(circuitBreakerTimeoutRef.current);
        }

        circuitBreakerTimeoutRef.current = window.setTimeout(() => {
          console.log('Resetting circuit breaker after timeout');
          circuitBreakerOpenRef.current = false;
          consecutiveErrorsRef.current = 0;
          circuitBreakerTimeoutRef.current = null;
        }, 5 * 60 * 1000); // 5 minutes
      }
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }, [isAuthenticated, getToken, currentWorkspace, authErrorOccurred, setCurrentWorkspace, setWorkspaces, setIsLoading, setError, setAuthErrorOccurred]);

  // Load workspaces on mount if authenticated and periodically refresh
  useEffect(() => {
    // Create a flag to track if we should clear workspace data
    let shouldClearWorkspaces = false;
    let isMounted = true;
    let retryCount = 0;
    const MAX_RETRIES = 2; // Reduce max retries from 3 to 2
    const RETRY_DELAY = 5000; // Increase retry delay from 2 to 5 seconds

    const initializeWorkspaces = async () => {
      if (!isAuthenticated || authErrorOccurred || !isMounted) return;

      // Skip if workspaces are already loaded
      const hasWorkspacesInState = Array.isArray(workspaces) && workspaces.length > 0;
      if (hasWorkspacesInState) {
        console.log('WorkspaceContext: Workspaces already loaded, skipping initialization');
        return;
      }

      console.log(`WorkspaceContext: Initializing workspaces (attempt ${retryCount + 1}/${MAX_RETRIES})`);

      try {
        await fetchWorkspaces(true); // Force fetch

        // Check if we got workspaces
        if (isMounted && !(Array.isArray(workspaces) && workspaces.length > 0) && retryCount < MAX_RETRIES - 1) {
          console.log(`WorkspaceContext: No workspaces found, retrying in ${RETRY_DELAY}ms`);
          retryCount++;
          setTimeout(initializeWorkspaces, RETRY_DELAY);
        }
      } catch (error) {
        console.error('Error initializing workspaces:', error);

        if (isMounted && retryCount < MAX_RETRIES - 1) {
          console.log(`WorkspaceContext: Error fetching workspaces, retrying in ${RETRY_DELAY}ms`);
          retryCount++;
          setTimeout(initializeWorkspaces, RETRY_DELAY);
        }
      }
    };

    if (isAuthenticated && !authErrorOccurred) {
      // Skip if workspaces are already loaded
      const hasWorkspacesInState = Array.isArray(workspaces) && workspaces.length > 0;

      if (!hasWorkspacesInState) {
        console.log('WorkspaceContext: User is authenticated, fetching workspaces');
        initializeWorkspaces(); // Use the more robust initialization
      } else {
        console.log('WorkspaceContext: Workspaces already loaded, skipping initial fetch');
      }

      // Set up a refresh interval to keep workspace data fresh
      // Only enable periodic refresh in production or when explicitly enabled
      const enablePeriodicRefresh = process.env.NODE_ENV === 'production' ||
                                   process.env.REACT_APP_ENABLE_PERIODIC_REFRESH === 'true';

      let refreshInterval: NodeJS.Timeout | null = null;

      if (enablePeriodicRefresh) {
        refreshInterval = setInterval(() => {
          if (isAuthenticated && !authErrorOccurred) {
            // Only refresh if not already loading and circuit breaker is not open
            if (!isLoadingRef.current && !circuitBreakerOpenRef.current) {
              console.log(`WorkspaceContext: Periodic refresh at ${new Date().toISOString()}`);
              // Use a non-forced fetch which will respect all throttling rules
              fetchWorkspaces();
            } else if (isLoadingRef.current) {
              console.log('WorkspaceContext: Skipping periodic refresh - already loading');
            } else if (circuitBreakerOpenRef.current) {
              console.log('WorkspaceContext: Skipping periodic refresh - circuit breaker open');
            }
          }
        }, 3600000); // Refresh every 60 minutes (increased from 30 minutes)
      } else {
        console.log('WorkspaceContext: Periodic refresh disabled in development mode');
      }

      return () => {
        isMounted = false;
        if (refreshInterval) {
          clearInterval(refreshInterval);
        }
      };
    } else {
      // Only clear workspace data if we haven't already
      shouldClearWorkspaces = true;
    }

    // Clear workspace data outside the conditional to avoid infinite loops
    if (shouldClearWorkspaces && (workspaces.length > 0 || currentWorkspace !== null)) {
      console.log('WorkspaceContext: User is not authenticated or auth error occurred, clearing workspace data');
      setWorkspaces([]);
      setCurrentWorkspace(null);
    }

    return () => {
      isMounted = false;
    };
  }, [isAuthenticated, authErrorOccurred, fetchWorkspaces, workspaces, currentWorkspace]);

  // No longer loading workspace from localStorage - we'll always fetch from the server

  // Notify components when current workspace changes
  useEffect(() => {
    if (currentWorkspace) {
      console.log(`Current workspace changed to: ${currentWorkspace.name} (ID: ${currentWorkspace.workspace_id})`);

      // Save the workspace ID to localStorage for persistence between sessions
      try {
        localStorage.setItem('lastWorkspaceId', currentWorkspace.workspace_id.toString());
      } catch (err) {
        console.error('Error saving workspace ID to localStorage:', err);
      }

      // Trigger a custom event to notify other components about the workspace change
      const workspaceChangeEvent = new CustomEvent('workspace-changed', {
        detail: { workspaceId: currentWorkspace.workspace_id }
      });
      window.dispatchEvent(workspaceChangeEvent);
    }
  }, [currentWorkspace]);

  // Use initialWorkspaceSet ref to track if we've already set the initial workspace

  // Set first workspace as current if none is selected and workspaces are loaded
  useEffect(() => {
    // Only run this effect if authenticated and we haven't set the initial workspace yet
    if (isAuthenticated && !currentWorkspace && workspaces.length > 0 && !initialWorkspaceSet.current) {
      initialWorkspaceSet.current = true;
      setCurrentWorkspace(workspaces[0]);
    }
  }, [workspaces, currentWorkspace, isAuthenticated]);

  // Listen for workspace data change events and rate limit errors
  useEffect(() => {
    const handleWorkspaceDataChanged = () => {
      if (!authErrorOccurred) {
        console.log('Workspace data change detected, refreshing workspaces');
        fetchWorkspaces();
      }
    };

    const handleRateLimitError = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.endpoint && customEvent.detail.endpoint.includes('/workspaces')) {
        console.warn('Rate limit error detected for workspace endpoint');

        // Open the circuit breaker to prevent more requests
        circuitBreakerOpenRef.current = true;

        // Set a timeout to close the circuit breaker after 2 minutes
        if (circuitBreakerTimeoutRef.current) {
          clearTimeout(circuitBreakerTimeoutRef.current);
        }

        circuitBreakerTimeoutRef.current = window.setTimeout(() => {
          console.log('Circuit breaker timeout elapsed, resetting circuit breaker');
          circuitBreakerOpenRef.current = false;
          circuitBreakerTimeoutRef.current = null;
        }, 120000); // 2 minutes

        // Show error to user
        setError('Too many requests. Please try again in a few minutes.');
      }
    };

    // Add event listeners
    window.addEventListener('workspace-data-changed', handleWorkspaceDataChanged);
    window.addEventListener('rate-limit-error', handleRateLimitError);

    // Clean up
    return () => {
      window.removeEventListener('workspace-data-changed', handleWorkspaceDataChanged);
      window.removeEventListener('rate-limit-error', handleRateLimitError);
    };
  }, [fetchWorkspaces, authErrorOccurred]);

  const createWorkspace = async (name: string, description?: string): Promise<Workspace> => {
    if (!isAuthenticated) throw new Error('Not authenticated');

    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      const newWorkspace = await apiPost<Workspace>('/workspaces', { name, description }, token);
      setWorkspaces(prev => [...prev, newWorkspace]);

      // If this is the first workspace, set it as current
      if (workspaces.length === 0) {
        setCurrentWorkspace(newWorkspace);
      }

      return newWorkspace;
    } catch (err) {
      console.error('Error creating workspace:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  };

  const updateWorkspace = async (id: number, name: string, description?: string): Promise<Workspace> => {
    if (!isAuthenticated) throw new Error('Not authenticated');

    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      const updatedWorkspace = await apiPut<Workspace>(`/workspaces/${id}`, { name, description }, token);

      // Update workspaces list
      setWorkspaces(prev =>
        prev.map(workspace =>
          workspace.workspace_id === id ? updatedWorkspace : workspace
        )
      );

      // Update current workspace if it's the one being updated
      if (currentWorkspace?.workspace_id === id) {
        setCurrentWorkspace(updatedWorkspace);
      }

      return updatedWorkspace;
    } catch (err) {
      console.error('Error updating workspace:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  };

  const deleteWorkspace = async (id: number): Promise<void> => {
    if (!isAuthenticated) throw new Error('Not authenticated');

    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      await apiDelete(`/workspaces/${id}`, token);

      // Remove from workspaces list
      const updatedWorkspaces = workspaces.filter(workspace => workspace.workspace_id !== id);
      setWorkspaces(updatedWorkspaces);

      // If current workspace was deleted, set a new one
      if (currentWorkspace?.workspace_id === id) {
        if (updatedWorkspaces.length > 0) {
          setCurrentWorkspace(updatedWorkspaces[0]);
        } else {
          setCurrentWorkspace(null);
          localStorage.removeItem('currentWorkspace');
          // Redirect to workspace creation if no workspaces left
          navigate('/workspace/create');
        }
      }
    } catch (err) {
      console.error('Error deleting workspace:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  };

  const addUserToWorkspace = async (workspaceId: number, userId: number, role: string): Promise<WorkspaceUser> => {
    if (!isAuthenticated) throw new Error('Not authenticated');

    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      return await apiPost<WorkspaceUser>(
        `/workspaces/${workspaceId}/users`,
        { user_id: userId, role },
        token
      );
    } catch (err) {
      console.error('Error adding user to workspace:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  };

  const removeUserFromWorkspace = async (workspaceId: number, userId: number): Promise<void> => {
    if (!isAuthenticated) throw new Error('Not authenticated');

    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      await apiDelete(`/workspaces/${workspaceId}/users/${userId}`, token);
    } catch (err) {
      console.error('Error removing user from workspace:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  };

  const updateUserRole = async (workspaceId: number, userId: number, role: string): Promise<WorkspaceUser> => {
    if (!isAuthenticated) throw new Error('Not authenticated');

    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      return await apiPut<WorkspaceUser>(
        `/workspaces/${workspaceId}/users/${userId}`,
        { role },
        token
      );
    } catch (err) {
      console.error('Error updating user role:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  };

  const getWorkspaceUsers = async (workspaceId: number): Promise<WorkspaceUser[]> => {
    if (!isAuthenticated) throw new Error('Not authenticated');

    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      return await apiGet<WorkspaceUser[]>(`/workspaces/${workspaceId}/users`, token);
    } catch (err) {
      console.error('Error getting workspace users:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      throw err;
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  };

  // Function to switch the current workspace - simplified approach
  const switchWorkspace = useCallback((workspace: Workspace) => {
    // Skip if workspace is null or undefined
    if (!workspace) {
      console.warn('WorkspaceContext: Attempted to switch to null/undefined workspace');
      return;
    }

    // Skip if trying to switch to the current workspace
    if (currentWorkspace?.workspace_id === workspace.workspace_id) {
      console.log(`WorkspaceContext: Already on workspace ${workspace.name} (ID: ${workspace.workspace_id}), skipping switch`);
      return;
    }

    console.log(`WorkspaceContext: Switching to workspace ${workspace.name} (ID: ${workspace.workspace_id}) from ${currentWorkspace?.name || 'none'}`);

    // Save the workspace ID to localStorage for persistence between sessions
    try {
      localStorage.setItem('lastWorkspaceId', workspace.workspace_id.toString());
      localStorage.setItem('workspaceSwitchTimestamp', Date.now().toString());
    } catch (err) {
      console.error('Error saving workspace ID to localStorage:', err);
    }

    // Use a single event for workspace switching
    // This simplifies the event system and reduces potential race conditions
    const workspaceSwitchEvent = new CustomEvent('workspace-switch', {
      detail: {
        previousWorkspaceId: currentWorkspace?.workspace_id,
        newWorkspaceId: workspace.workspace_id,
        newWorkspaceName: workspace.name
      }
    });

    // Set the new workspace directly
    setCurrentWorkspace(workspace);

    // Dispatch the event after setting the workspace
    window.dispatchEvent(workspaceSwitchEvent);

    console.log(`WorkspaceContext: Workspace switch complete to ${workspace.name} (ID: ${workspace.workspace_id})`);
  }, [currentWorkspace]);

  // Function to clear workspace cache and refresh data
  const clearWorkspaceCache = useCallback(async () => {
    console.log('Clearing workspace cache and refreshing data');

    // Clear the lastWorkspaceId from localStorage
    try {
      localStorage.removeItem('lastWorkspaceId');
    } catch (err) {
      console.error('Error removing lastWorkspaceId from localStorage:', err);
    }

    // Reset all state and refs
    setWorkspaces([]); // Clear workspaces in state
    setCurrentWorkspace(null); // Clear current workspace in state
    setAuthErrorOccurred(false); // Reset auth error flag to allow fetching again
    consecutiveErrorsRef.current = 0; // Reset error counter
    circuitBreakerOpenRef.current = false; // Reset circuit breaker

    // Clear any pending circuit breaker timeout
    if (circuitBreakerTimeoutRef.current) {
      clearTimeout(circuitBreakerTimeoutRef.current);
      circuitBreakerTimeoutRef.current = null;
    }

    // Fetch fresh data with force=true to bypass throttling
    await fetchWorkspaces(true);
  }, [fetchWorkspaces, setWorkspaces, setCurrentWorkspace, setAuthErrorOccurred]);

  // Function to get diagnostic information about the workspace context
  const getWorkspaceDiagnostics = useCallback(() => {
    return {
      workspaceCount: workspaces.length,
      currentWorkspaceId: currentWorkspace?.workspace_id,
      isLoading: isLoading,
      isLoadingRef: isLoadingRef.current,
      error: error,
      authErrorOccurred: authErrorOccurred,
      lastFetchTime: lastFetchTimeRef.current ? new Date(lastFetchTimeRef.current).toISOString() : null,
      timeSinceLastFetch: lastFetchTimeRef.current ? Math.round((Date.now() - lastFetchTimeRef.current) / 1000) + 's' : 'never',
      consecutiveErrors: consecutiveErrorsRef.current,
      circuitBreakerOpen: circuitBreakerOpenRef.current,
      localStorageWorkspaceId: localStorage.getItem('lastWorkspaceId'),
      initialWorkspaceSet: initialWorkspaceSet.current
    };
  }, [workspaces, currentWorkspace, isLoading, error, authErrorOccurred]);

  const value = {
    workspaces,
    currentWorkspace,
    isLoading,
    error,
    fetchWorkspaces,
    setCurrentWorkspace,
    switchWorkspace,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    addUserToWorkspace,
    removeUserFromWorkspace,
    updateUserRole,
    getWorkspaceUsers,
    clearWorkspaceCache,
    getWorkspaceDiagnostics // Add diagnostic function
  };

  // Expose the workspace context to the window object for testing
  if (typeof window !== 'undefined') {
    // Use type assertion to avoid TypeScript error
    (window as any).__WORKSPACE_CONTEXT__ = value;
  }

  return (
    <WorkspaceContext.Provider value={value}>
      {children}
    </WorkspaceContext.Provider>
  );
};

export const useWorkspace = (): WorkspaceContextType => {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
};
