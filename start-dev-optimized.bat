@echo off

REM Start Expense Logger in development mode with optimized settings for low CPU usage
echo Starting Expense Logger in development mode with CPU optimizations...

REM Check if docker-compose.override.yml exists
if not exist "docker-compose.override.yml" (
    echo Error: docker-compose.override.yml not found!
    echo This file is required for optimized development settings.
    pause
    exit /b 1
)

REM Start the services
echo Starting services with optimized settings...
docker-compose up --build

echo Services started with the following optimizations:
echo - Frontend: Fast refresh disabled, file polling disabled
echo - Backend: Reload delay increased to 2 seconds
echo - Workspace diagnostics: Update interval set to 5 seconds
echo - Periodic workspace refresh: Disabled in development
pause
