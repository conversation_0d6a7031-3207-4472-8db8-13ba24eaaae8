# Development environment configuration with optimizations for faster startup

# API URL - development backend server
REACT_APP_API_URL=http://localhost:8000

# Environment name
REACT_APP_ENV=development

# Feature flags
REACT_APP_ENABLE_OCR_TEMPLATES=true
REACT_APP_ENABLE_WISHLIST=true
REACT_APP_ENABLE_DARK_MODE=true

# Authentication settings
REACT_APP_AUTH_STORAGE_KEY=expense_logger_auth
REACT_APP_AUTH_TOKEN_EXPIRY=86400 # 24 hours in seconds
REACT_APP_AUTH_REFRESH_THRESHOLD=3600 # 1 hour in seconds

# Analytics and monitoring (disabled in development)
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_SENTRY_DSN=

# Performance settings (disabled in development to reduce CPU usage)
REACT_APP_ENABLE_PERIODIC_REFRESH=false
REACT_APP_DIAGNOSTICS_UPDATE_INTERVAL=5000

# Development tools and optimizations
REACT_APP_ENABLE_MOCK_API=false
REACT_APP_SHOW_DEBUG_INFO=false
DISABLE_ESLINT_PLUGIN=true
TSC_COMPILE_ON_ERROR=true
FAST_REFRESH=true
CHOKIDAR_USEPOLLING=true
WDS_SOCKET_PORT=0
BROWSER=none
GENERATE_SOURCEMAP=false
SKIP_PREFLIGHT_CHECK=true

# Upload limits
REACT_APP_MAX_UPLOAD_SIZE=10485760 # 10 MB in bytes
REACT_APP_ALLOWED_FILE_TYPES=.pdf,.png,.jpg,.jpeg

# Application defaults
REACT_APP_DEFAULT_CURRENCY=USD
REACT_APP_DEFAULT_DATE_FORMAT=MM/DD/YYYY
REACT_APP_DEFAULT_LANGUAGE=en-US
