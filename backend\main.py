# backend/main.py
import os
import datetime
from pathlib import Path
from fastapi import FastAPI, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session

# Import core components
from core.database import engine, Base, get_db
from core.config import DATABASE_URL
from core.rate_limiter import RateLimiter

# Import feature routers
from features.auth.router import router as users_router
from features.auth.auth_router import router as auth_router
from features.auth.users_router import router as auth_users_router
from features.setup.router import router as setup_router
from features.invoices.router import router as invoices_router
from features.payments.router import router as payments_router
from features.expenses.router import router as expenses_router
from features.templates.router import router as templates_router
from features.ocr.router import router as ocr_router
from features.wishlist.router import router as wishlist_router
from features.exports.router import router as exports_router
from features.workspace.router import router as workspace_router
from features.exports.integrated_export import router as integrated_exports_router
from features.exports.export_utils import router as export_utils_router
from features.admin.router import router as admin_router
from features.dashboard.router import router as dashboard_router
from features.admin.sql_security_router import router as sql_security_router
from features.admin.sql_query_router import router as sql_query_router

# Create the FastAPI application with increased request size limit
app = FastAPI(
    title="Invoice Management System",
    # Maximum size of a request in bytes (10MB)
    max_request_size=10 * 1024 * 1024
)

# Ensure the upload folder path is inside the database directory
# This will be created during setup when the database type is selected
DATABASE_DIR = Path("database")
UPLOAD_FOLDER = DATABASE_DIR / "uploads"

# Always create directories regardless of setup status
DATABASE_DIR.mkdir(parents=True, exist_ok=True)
UPLOAD_FOLDER.mkdir(parents=True, exist_ok=True)
# Mount it for static file serving
app.mount("/uploads", StaticFiles(directory=UPLOAD_FOLDER), name="uploads")
print("Created database directories and mounted uploads folder")

# Configure CORS with specific origins to allow credentials
# Include all possible frontend origins
origins = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://frontend:3000",
    "http://**********:3000",
    "http://**********:3000",
    "http://**********:3000",
    "http://**********:3000",
    # Add the actual hostname for production
    "http://localhost:8000"
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,  # Allow credentials with specific origins
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limiter middleware for workspace endpoints
# Allow 5 requests per minute per user for workspace endpoints
app.add_middleware(RateLimiter, rate_limit_per_minute=5)

# Include routers from features
app.include_router(setup_router)
app.include_router(auth_router)
app.include_router(users_router)
app.include_router(auth_users_router)
app.include_router(invoices_router)
app.include_router(payments_router)
app.include_router(expenses_router)
app.include_router(templates_router)
app.include_router(ocr_router)
app.include_router(wishlist_router)
app.include_router(exports_router)
app.include_router(integrated_exports_router)
app.include_router(export_utils_router)
app.include_router(admin_router)
app.include_router(workspace_router)
app.include_router(dashboard_router)
app.include_router(sql_security_router)
app.include_router(sql_query_router)

# Import models at module level to speed up startup
from features.auth.models import User
from features.workspace.models import Workspace, WorkspaceUser
import features.invoices.models
import features.payments.models
import features.templates.models
import features.wishlist.models

# Flag to track if we've already run migrations
migrations_run = False

# Create tables on startup
@app.on_event("startup")
async def startup_event():
    global migrations_run

    # Check if we should skip database operations for faster startup
    fast_startup = os.environ.get('FAST_STARTUP', '').lower()
    force_migrations = os.environ.get('FORCE_MIGRATIONS', '').lower() == 'true'

    if fast_startup == 'true' and not force_migrations:
        print("Fast startup enabled and no force migrations, skipping database operations")
        return
    else:
        if force_migrations:
            print("Force migrations enabled, running database operations regardless of fast startup setting")
        else:
            print(f"Fast startup disabled (value: '{fast_startup}'), running database operations")

    # Create tables for both SQLite and PostgreSQL
    try:
        print(f"Creating tables in database: {DATABASE_URL}")
        Base.metadata.create_all(bind=engine)
        print("Tables created successfully")
    except Exception as e:
        print(f"Error creating tables: {e}")

    # Run migrations if needed
    if not migrations_run or os.environ.get('FORCE_MIGRATIONS', '').lower() == 'true':
        try:
            # Run all migrations using the migrations module
            import sys
            import sqlalchemy as sa

            # Get the absolute path to the migrations directory
            migrations_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "database", "migrations")
            print(f"Looking for migrations in: {migrations_dir}")

            if os.path.exists(migrations_dir):
                # Apply SQL migrations manually
                for filename in sorted(os.listdir(migrations_dir)):
                    if filename.endswith('.sql'):
                        migration_path = os.path.join(migrations_dir, filename)
                        print(f"Applying migration: {filename}")

                        try:
                            with open(migration_path, 'r') as f:
                                sql = f.read()

                            with engine.begin() as conn:
                                conn.execute(sa.text(sql))

                            print(f"Successfully applied migration: {filename}")
                        except Exception as e:
                            print(f"Error applying migration {filename}: {str(e)}")

                migrations_run = True
                print("SQL migrations completed successfully")
            else:
                print(f"Migrations directory not found: {migrations_dir}")
        except Exception as migration_error:
            print(f"Error running SQL migrations: {migration_error}")

    # Check if template loading should be skipped
    if os.environ.get('SKIP_TEMPLATE_LOADING') == 'true':
        print("Template loading skipped due to SKIP_TEMPLATE_LOADING environment variable")
        return

    # Get database connection
    db = next(get_db())
    try:
        # Check if any users exist before loading templates
        user_count = db.query(User).count()
        if user_count > 0:
            # Only load templates if users exist (setup is complete)
            from pathlib import Path
            from utils.load_templates import load_templates_from_folder

            base_dir = Path(__file__).resolve().parent
            templates_folder = base_dir / "documentation" / "Temp"

            if templates_folder.exists():
                print(f"Loading templates from {templates_folder}")
                # Use the first user's ID for template creation
                first_user = db.query(User).first()
                if first_user:
                    # Check if we need to load templates by comparing file modification times
                    from features.templates.models import InvoiceTemplate

                    # Get the latest template update time from the database
                    latest_template = db.query(InvoiceTemplate).order_by(InvoiceTemplate.updated_at.desc()).first()
                    latest_db_time = latest_template.updated_at if latest_template else None

                    # Check if any template files are newer than the latest database entry
                    need_update = False
                    if not latest_db_time:
                        need_update = True
                    else:
                        for file_name in os.listdir(str(templates_folder)):
                            if file_name.endswith('.json'):
                                file_path = os.path.join(str(templates_folder), file_name)
                                file_mtime = os.path.getmtime(file_path)
                                file_mtime_dt = datetime.datetime.fromtimestamp(file_mtime)
                                if not latest_db_time or file_mtime_dt > latest_db_time:
                                    need_update = True
                                    break

                    if need_update:
                        load_templates_from_folder(str(templates_folder), db, first_user.user_id)
                        print("Templates loaded successfully")
                    else:
                        print("Templates are up to date, skipping reload")
                else:
                    print("No users found to associate with templates")
            else:
                print(f"Templates folder not found: {templates_folder}")
        else:
            print("Skipping template loading - no users exist yet")
    except Exception as e:
        db.rollback()
        print(f"Error during startup: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        # Set timeout and limits for uploads
        timeout_keep_alive=300,
        limit_concurrency=10,
        limit_max_requests=100
    )