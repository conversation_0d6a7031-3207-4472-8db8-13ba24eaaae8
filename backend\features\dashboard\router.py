"""Dashboard router for dashboard-related endpoints."""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, extract, desc
from typing import List, Optional
from datetime import datetime, timedelta
import calendar

from core.database import get_db
from features.auth.auth_router import get_current_user
from features.auth.models import User
from features.invoices.models import Invoice, InvoiceItem, Category, Tag
from features.workspace.utils import has_workspace_access
from features.workspace.models import Workspace

# Define response models
from pydantic import BaseModel

class DashboardStats(BaseModel):
    """Dashboard statistics response model."""
    totalInvoices: int
    totalAmount: float
    openInvoices: int
    paidInvoices: int
    averageInvoiceAmount: float

class ChartDataPoint(BaseModel):
    """Chart data point for category and payment method charts."""
    name: str
    value: float

class MonthlyDataPoint(BaseModel):
    """Monthly data point for monthly spending chart."""
    month: str
    amount: float

class StatusChartData(BaseModel):
    """Status chart data point."""
    name: str
    value: int

router = APIRouter(
    prefix="/dashboard",
    tags=["dashboard"],
    responses={404: {"description": "Not found"}},
)

@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    workspace_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get dashboard statistics."""
    # Check workspace access if workspace_id is provided
    if workspace_id:
        if not has_workspace_access(db, current_user.user_id, workspace_id):
            raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    # Base query
    query = db.query(Invoice).filter(Invoice.is_deleted == False)

    # Apply workspace filter if provided
    if workspace_id:
        query = query.filter(Invoice.workspace_id == workspace_id)

    # Get all invoices
    invoices = query.all()

    # Calculate statistics
    total_invoices = len(invoices)
    total_amount = sum(float(invoice.grand_total or 0) for invoice in invoices)
    open_invoices = sum(1 for invoice in invoices if invoice.status == "Open")
    paid_invoices = sum(1 for invoice in invoices if invoice.status == "Paid")
    avg_amount = total_amount / total_invoices if total_invoices > 0 else 0

    return DashboardStats(
        totalInvoices=total_invoices,
        totalAmount=total_amount,
        openInvoices=open_invoices,
        paidInvoices=paid_invoices,
        averageInvoiceAmount=avg_amount
    )

@router.get("/monthly-spending", response_model=List[MonthlyDataPoint])
async def get_monthly_spending(
    year: str = Query("current", description="Year to filter by, or 'current' for current year"),
    workspace_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get monthly spending data for charts."""
    # Check workspace access if workspace_id is provided
    if workspace_id:
        if not has_workspace_access(db, current_user.user_id, workspace_id):
            raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    # Determine the year to filter by
    filter_year = datetime.now().year
    if year != "current" and year.isdigit():
        filter_year = int(year)

    # Base query
    query = db.query(
        extract('month', Invoice.purchase_date).label('month'),
        func.sum(Invoice.grand_total).label('amount')
    ).filter(
        Invoice.is_deleted == False,
        extract('year', Invoice.purchase_date) == filter_year
    )

    # Apply workspace filter if provided
    if workspace_id:
        query = query.filter(Invoice.workspace_id == workspace_id)

    # Group by month and get results
    monthly_data = query.group_by(extract('month', Invoice.purchase_date)).all()

    # Format the results
    result = []
    for month_num, amount in monthly_data:
        month_name = calendar.month_name[int(month_num)]
        result.append(MonthlyDataPoint(month=month_name, amount=float(amount or 0)))

    # Sort by month
    result.sort(key=lambda x: list(calendar.month_name).index(x.month) if x.month in calendar.month_name else 0)

    return result

@router.get("/spending-by-category", response_model=List[ChartDataPoint])
async def get_spending_by_category(
    workspace_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get spending by category for charts."""
    # Check workspace access if workspace_id is provided
    if workspace_id:
        if not has_workspace_access(db, current_user.user_id, workspace_id):
            raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    # Base query to get all invoices
    query = db.query(Invoice).filter(Invoice.is_deleted == False)

    # Apply workspace filter if provided
    if workspace_id:
        query = query.filter(Invoice.workspace_id == workspace_id)

    # Get all invoices
    invoices = query.all()

    # Process and group by category
    category_data = {}
    for invoice in invoices:
        # For now, since categories relationship is disabled, group by merchant or use "General"
        # TODO: Re-enable categories relationship once model imports are fixed
        try:
            categories = getattr(invoice, 'categories', None) or []
        except:
            categories = []

        if not categories:
            # Use merchant name as category, or "Uncategorized" if no merchant
            category_name = invoice.merchant_name or "Uncategorized"
            if category_name not in category_data:
                category_data[category_name] = 0
            category_data[category_name] += float(invoice.grand_total or 0)
        else:
            # Add to each category
            for category in categories:
                category_name = category if isinstance(category, str) else getattr(category, 'category_name', str(category))
                if category_name not in category_data:
                    category_data[category_name] = 0
                category_data[category_name] += float(invoice.grand_total or 0) / len(categories)

    # Format the results
    result = [
        ChartDataPoint(name=category, value=amount)
        for category, amount in category_data.items()
    ]

    # Sort by amount (descending)
    result.sort(key=lambda x: x.value, reverse=True)

    return result

@router.get("/status-distribution", response_model=List[StatusChartData])
async def get_status_distribution(
    workspace_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get invoice status distribution for charts."""
    # Check workspace access if workspace_id is provided
    if workspace_id:
        if not has_workspace_access(db, current_user.user_id, workspace_id):
            raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    # Base query
    query = db.query(
        Invoice.status,
        func.count(Invoice.invoice_id).label('count')
    ).filter(Invoice.is_deleted == False)

    # Apply workspace filter if provided
    if workspace_id:
        query = query.filter(Invoice.workspace_id == workspace_id)

    # Group by status and get results
    status_data = query.group_by(Invoice.status).all()

    # Format the results
    result = [
        StatusChartData(name=status or "Unknown", value=count)
        for status, count in status_data
    ]

    return result

@router.get("/payment-method-distribution", response_model=List[ChartDataPoint])
async def get_payment_method_distribution(
    workspace_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get payment method distribution for charts."""
    # Check workspace access if workspace_id is provided
    if workspace_id:
        if not has_workspace_access(db, current_user.user_id, workspace_id):
            raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    # Base query
    query = db.query(
        Invoice.payment_method,
        func.sum(Invoice.grand_total).label('amount')
    ).filter(Invoice.is_deleted == False)

    # Apply workspace filter if provided
    if workspace_id:
        query = query.filter(Invoice.workspace_id == workspace_id)

    # Group by payment method and get results
    payment_data = query.group_by(Invoice.payment_method).all()

    # Format the results
    result = [
        ChartDataPoint(name=payment_method or "Unknown", value=float(amount or 0))
        for payment_method, amount in payment_data
    ]

    # Sort by amount (descending)
    result.sort(key=lambda x: x.value, reverse=True)

    return result

# We've moved this endpoint to /invoices/recent in the invoices router
