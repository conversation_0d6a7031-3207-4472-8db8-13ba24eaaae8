#!/usr/bin/env python3

import requests
import json
import sys

def test_dashboard_endpoints():
    base_url = 'http://localhost:8000'
    workspace_id = 1
    
    endpoints = [
        f'/dashboard/stats?workspace_id={workspace_id}',
        f'/dashboard/monthly-spending?year=2025&workspace_id={workspace_id}',
        f'/dashboard/spending-by-category?workspace_id={workspace_id}',
        f'/dashboard/invoice-status-distribution?workspace_id={workspace_id}',
        f'/dashboard/payment-method-distribution?workspace_id={workspace_id}',
        f'/invoices/recent?limit=5&workspace_id={workspace_id}'
    ]
    
    print("=== Testing Dashboard API Endpoints ===\n")
    
    for endpoint in endpoints:
        try:
            print(f"Testing: {endpoint}")
            response = requests.get(f'{base_url}{endpoint}', timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"Response: {json.dumps(data, indent=2)}")
                except json.JSONDecodeError:
                    print(f"Response (text): {response.text}")
            else:
                print(f"Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
        
        print("-" * 50)

if __name__ == "__main__":
    test_dashboard_endpoints()
