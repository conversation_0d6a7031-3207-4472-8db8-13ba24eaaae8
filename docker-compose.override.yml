services:
  frontend:
    environment:
      - DISABLE_ESLINT_PLUGIN=true
      - FAST_REFRESH=false # Disable fast refresh to reduce CPU usage
      - CHOKIDAR_USEPOLLING=false # Disable polling to reduce CPU usage
      - CHOKIDAR_INTERVAL=2000 # Increase polling interval if polling is needed
      - WDS_SOCKET_PORT=0
      - BROWSER=none # Don't open browser automatically
      # Add environment variable to control periodic refresh
      - REACT_APP_ENABLE_PERIODIC_REFRESH=false
    command: sh -c "npm start"

  backend:
    environment:
      - SKIP_TEMPLATE_LOADING=true
      - FAST_STARTUP=true
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload --workers 1 --reload-delay 2
