# Docker Compose file for Expense Logger
# The version key is deprecated in Compose Specification
# Using latest Compose format

services:
  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: expense_logger_api
    volumes:
      - ./backend:/app
      - ./documentation/Temp:/app/documentation/Temp
      - config_data:/app/config
      - database_data:/app/database
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      # Add environment variable for max upload size
      - MAX_UPLOAD_SIZE=10485760 # 10MB in bytes
      # Database connection settings - using the same password as in the postgres service
      - DB_HOST=postgres_db
      - DB_NAME=expense_logger
      - DB_USER=postgres
      - DB_PASSWORD=admin
      # Disable fast startup to run migrations (must be 'false' not true)
      - FAST_STARTUP=false
      - SKIP_TEMPLATE_LOADING=false
      - FORCE_MIGRATIONS=true
      # Email configuration (disabled by default, enable and configure for password reset)
      - EMAIL_ENABLED=true
      - SMTP_SERVER=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USERNAME=<EMAIL>
      - SMTP_PASSWORD=test_password
      - EMAIL_FROM=<EMAIL>
    # PostgreSQL is now optional since we support SQLite
    restart: unless-stopped
    # Command with optimized settings for development
    command: >
      uvicorn main:app --host 0.0.0.0 --port 8000 --reload --timeout-keep-alive 120 --reload-delay 2 --workers 1

  # Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: expense_logger_frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    restart: unless-stopped
    volumes:
      - ./frontend:/app
      - /app/node_modules

  # Database service (optional - can use SQLite instead)
  postgres_db:
    image: postgres:15-alpine
    container_name: expense_logger_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=admin
      - POSTGRES_DB=expense_logger
    restart: unless-stopped

volumes:
  postgres_data:
  config_data:
  database_data:
