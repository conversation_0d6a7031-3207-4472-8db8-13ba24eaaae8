// src/features/dashboard/Dashboard.tsx
import React, { useEffect, useState } from "react";
import DashboardFilters from "./DashboardFilters";
import StatsCards from "./StatsCards";
import SpendingCharts from "./SpendingChart";
import CategoryCharts from "./CategoryCharts";
import RecentInvoices from "./RecentInvoices";
import "./dashboard.css";
import "./dashboard-dark-mode.css"; // Import dark mode specific styles
import { useWorkspace } from "../workspace/workspaceContext";
import {
  fetchDashboardStats,
  fetchRecentInvoices,
  fetchMonthlySpending,
  fetchSpendingByCategory,
  fetchInvoiceStatusDistribution,
  fetchPaymentMethodDistribution
} from "./dashboardApi";

// Define TypeScript interfaces
import {
  Invoice,
  ChartData,
  DashboardStats,
  ChartDataPoint,
  MonthlyDataPoint
} from "./types";

const Dashboard: React.FC = () => {
  // Get the current workspace from context
  const { currentWorkspace } = useWorkspace();

  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [allInvoices, setAllInvoices] = useState<Invoice[]>([]); // Keep a reference to all invoices for Recent Invoices section
  // We no longer need to track categories and tags separately
  const [selectedYear, setSelectedYear] = useState<number | string>(new Date().getFullYear());
  const [selectedStatus, setSelectedStatus] = useState<string>("All");
  const [loading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeChart, setActiveChart] = useState<'monthly' | 'area'>('monthly');
  const [stats, setStats] = useState<DashboardStats>({
    totalInvoices: 0,
    totalAmount: 0,
    openInvoices: 0,
    paidInvoices: 0,
    averageInvoiceAmount: 0
  });
  const [chartData, setChartData] = useState<ChartData>({
    monthly: [],
    category: [],
    status: [],
    paymentMethod: []
  });

  // Fetch all data on mount and when workspace changes
  useEffect(() => {
    console.log(`Dashboard: Loading data for workspace ${currentWorkspace?.name} (ID: ${currentWorkspace?.workspace_id})`);
    loadDashboardData();
  }, [currentWorkspace]);

  // Load dashboard data from API
  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get the current workspace ID
      const workspaceId = currentWorkspace?.workspace_id || null;
      console.log(`Dashboard: Loading data for workspace ID: ${workspaceId}`);

      // Fetch all data in parallel with workspace filtering
      const [
        stats,
        recentInvoices,
        monthlyData,
        categoryData,
        statusData,
        paymentMethodData
      ] = await Promise.all([
        fetchDashboardStats(workspaceId),
        fetchRecentInvoices(5, workspaceId),
        fetchMonthlySpending(selectedYear, workspaceId),
        fetchSpendingByCategory(workspaceId),
        fetchInvoiceStatusDistribution(workspaceId),
        fetchPaymentMethodDistribution(workspaceId)
      ]);

      console.log('Dashboard: Received data:', {
        stats,
        recentInvoicesCount: recentInvoices?.length || 0,
        monthlyDataCount: monthlyData?.length || 0,
        categoryDataCount: categoryData?.length || 0,
        statusDataCount: statusData?.length || 0,
        paymentMethodDataCount: paymentMethodData?.length || 0
      });

      // Update state with fetched data
      setStats(stats);
      setAllInvoices(recentInvoices || []);
      setInvoices(recentInvoices || []);

      // Update chart data - ensure we have valid arrays
      setChartData({
        monthly: Array.isArray(monthlyData) ? monthlyData as unknown as MonthlyDataPoint[] : [],
        category: Array.isArray(categoryData) ? categoryData as unknown as ChartDataPoint[] : [],
        status: Array.isArray(statusData) ? statusData as unknown as ChartDataPoint[] : [],
        paymentMethod: Array.isArray(paymentMethodData) ? paymentMethodData as unknown as ChartDataPoint[] : []
      });

      setIsLoading(false);
    } catch (err) {
      console.error("Error loading dashboard data:", err);
      setError("Failed to load dashboard data. Please try again.");
      setIsLoading(false);
    }
  };

  // We now get filtered data directly from the API

  // Handle filter changes
  const handleFiltersChange = (year: string | number, status: string) => {
    setSelectedYear(year);
    setSelectedStatus(status);

    // Reload data with new filters
    loadDashboardData();
  };

  // We now get statistics directly from the API

  // We now get chart data directly from the API

  // Get unique years from invoices for the filter
  const getYears = (): number[] => {
    const yearSet = new Set<number>();
    // Add current year as default
    const currentYear = new Date().getFullYear();
    yearSet.add(currentYear);

    // Add years from invoices if available
    if (invoices && Array.isArray(invoices)) {
      invoices.forEach((inv) => {
        if (inv.purchase_date) {
          const year = new Date(inv.purchase_date).getFullYear();
          yearSet.add(year);
        }
      });
    }

    return Array.from(yearSet).sort((a, b) => b - a); // Sort descending
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-3 text-gray-600 dark:text-gray-400">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-400 p-4 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-red-700 dark:text-red-200">{error}</p>
              <div className="mt-2">
                <button
                  onClick={() => loadDashboardData()}
                  className="bg-red-100 hover:bg-red-200 dark:bg-red-800 dark:hover:bg-red-700 text-red-800 dark:text-red-200 px-3 py-1 rounded-md text-sm font-medium"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const years = getYears();

  return (
    <div className="dashboard max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-dark-text-primary">Dashboard</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => loadDashboardData()}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
          <button
            type="button"
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            Export
          </button>
        </div>
      </div>

      {/* Filters Component */}
      <DashboardFilters
        years={years}
        selectedYear={selectedYear}
        selectedStatus={selectedStatus}
        activeChart={activeChart}
        onYearChange={(year) => handleFiltersChange(year, selectedStatus)}
        onStatusChange={(status) => handleFiltersChange(selectedYear, status)}
        onChartTypeChange={setActiveChart}
      />

      {/* Stats Cards Component */}
      <StatsCards stats={stats || {
        totalInvoices: 0,
        totalAmount: 0,
        openInvoices: 0,
        paidInvoices: 0,
        averageInvoiceAmount: 0
      }} />

      {/* Spending Charts Component */}
      <SpendingCharts
        chartData={chartData.monthly}
        activeChartType={activeChart}
      />

      {/* Category Charts Component */}
      <CategoryCharts
        categoryData={chartData.category}
        statusData={chartData.status}
        paymentMethodData={chartData.paymentMethod}
      />

      {/* Recent Invoices Component - Now using allInvoices instead of filteredInvoices */}
      <RecentInvoices invoices={(allInvoices || []).slice(0, 5)} />
    </div>
  );
};

export default Dashboard;