# Expense Logger - Summary

Expense Logger is a self-hosted expense tracking application designed to help you monitor individual purchases through invoice receipts or manual inputs. It allows you to categorize each entry by item type, providing a detailed record of your spending habits.

![Dashboard](documentation/project_images/dashboard.png)

## Key Features

- **Itemized Expense Tracking**: Log each purchase with specific details, enabling precise monitoring of items such as HVAC filters or water heaters over extended periods.

- **Receipt and Document Management**: Attach PDF receipts and warranty documents to each entry, ensuring all relevant information is easily accessible for future reference, such as warranty claims.

![Expense Details](documentation/project_images/expense-details.png)

- **Categorization**: Organize expenses by categories and item types, facilitating analysis of spending patterns across different hobbies or household needs.

- **OCR Template System**: Extract information automatically from invoice PDFs using customizable templates for different merchants.

- **Workspace Management**: Organize your financial data into separate workspaces and control access for different users.

- **Flexible Database Options**: Choose between SQLite for simplicity or PostgreSQL for advanced multi-user scenarios.

This tool is particularly useful for individuals who wish to keep a comprehensive record of their purchases, track the frequency of specific item replacements, manage warranty information efficiently, and analyze spending across various interests over time.

# Expense Logger - Setup Guide

This guide provides comprehensive instructions for setting up the Expense Logger application using Docker with centralized environment configuration.

## Prerequisites

- Docker and Docker Compose installed on your system
- Git (for cloning the repository)

## Database Options

Expense Logger supports two database options:

1. **SQLite** (Recommended for individuals and small deployments)

   - Simpler setup with no additional services required
   - Lower resource usage
   - Single-user focused
   - File-based storage that's easy to back up

2. **PostgreSQL** (Recommended for multi-user or larger deployments)
   - Better performance with large datasets
   - Supports multiple concurrent users
   - More advanced query capabilities
   - Requires additional resources

You can choose your preferred database type during the initial setup process.

## Project Structure

```
expense-logger/
├── backend/
│   ├── Dockerfile
│   ├── main.py
│   └── requirements.txt
├── database/
│   └── uploads/
├── documentation/
│   └── Temp/
│       ├── bh.json
│       ├── dell.json
│       └── newegg.json
├── frontend/
│   ├── public/
│   ├── src/
│   ├── Dockerfile
│   └── package.json
├── scripts/
│   └── template_tests/
│       ├── docker_test_bh_template.py
│       ├── test_bh_template.py
│       └── test_script.py
├── .env
├── docker-compose.yml
└── README.md
```

## Setup Instructions

### 1. Clone the Repository

```bash
git clone <repository-url>
cd expense-logger
```

### 2. Configure Environment Variables

1. Copy the provided `.env` file to the root of the project:

```bash
# Example content of .env file
DB_HOST=postgres_db
DB_NAME=expense_logger
DB_USER=postgres
DB_PASSWORD=secret
# ... other variables
```

2. Review and modify the values as needed for your environment.

### 3. Build and Start the Services

```bash
# Build the Docker images
docker-compose build

# Start the services in the background
docker-compose up -d
```

### 4. Monitor the Setup Process

```bash
# View logs from all services
docker-compose logs -f
```

### 5. Complete the Setup Wizard

1. Access the application at http://localhost:3000
2. You'll be automatically redirected to the setup wizard
3. Follow the steps to:
   - Choose your database type (SQLite or PostgreSQL)
   - Configure database connection details (if using PostgreSQL)
   - Create an administrator account
   - Set up your first workspace

![Setup Wizard](documentation/project_images/setup-wizard.png)

The setup wizard will guide you through the entire process and configure the application based on your preferences.

### 6. Access the Application

After completing the setup:

- Frontend UI: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Environment Configuration

The central `.env` file contains configuration for all services. Note that database settings configured through the setup wizard will override these environment variables.

| Variable        | Description                       | Default                              | Notes                                |
| --------------- | --------------------------------- | ------------------------------------ | ------------------------------------ |
| DB_TYPE         | Database type                     | sqlite                               | Options: sqlite, postgres            |
| DB_HOST         | PostgreSQL server hostname        | postgres_db                          | Only used with PostgreSQL            |
| DB_PORT         | PostgreSQL server port            | 5432                                 | Only used with PostgreSQL            |
| DB_NAME         | Database name                     | expense_logger                       | Used for both SQLite and PostgreSQL  |
| DB_USER         | Database username                 | postgres                             | Only used with PostgreSQL            |
| DB_PASSWORD     | Database password                 | admin                                | Only used with PostgreSQL            |
| API_URL         | Backend API URL                   | http://localhost:8000                |                                      |
| API_PORT        | Port for backend service          | 8000                                 |                                      |
| FRONTEND_URL    | Frontend URL                      | http://localhost:3000                |                                      |
| FRONTEND_PORT   | Port for frontend service         | 3000                                 |                                      |
| JWT_SECRET      | Secret key for JWT tokens         | your-secret-key-change-in-production | Change this in production!           |
| MAX_UPLOAD_SIZE | Maximum file upload size in bytes | 10485760 (10MB)                      |                                      |
| EMAIL_ENABLED   | Enable email functionality        | true                                 | For password reset and notifications |
| SMTP_SERVER     | SMTP server for sending emails    | smtp.gmail.com                       | Only used if EMAIL_ENABLED is true   |
| SMTP_PORT       | SMTP port                         | 587                                  | Only used if EMAIL_ENABLED is true   |
| SMTP_USERNAME   | SMTP username                     | <EMAIL>                     | Only used if EMAIL_ENABLED is true   |
| SMTP_PASSWORD   | SMTP password                     | test_password                        | Only used if EMAIL_ENABLED is true   |
| EMAIL_FROM      | From address for emails           | <EMAIL>                     | Only used if EMAIL_ENABLED is true   |

## Development Workflow

### Performance Optimized Development

For reduced CPU usage during development, use the optimized startup scripts:

**Linux/Mac:**

```bash
./start-dev-optimized.sh
```

**Windows:**

```batch
start-dev-optimized.bat
```

These scripts apply optimizations that reduce idle CPU usage by 60-80% while maintaining development functionality. See [Performance Optimizations](documentation/PERFORMANCE_OPTIMIZATIONS.md) for details.

### Updating the Backend

1. Make changes to the backend code
2. The changes will be automatically applied (hot reload is enabled)

### Updating the Frontend

1. Make changes to the frontend code
2. The changes will be automatically applied (hot reload is enabled)

### OCR Templates

The application uses OCR templates to extract information from invoice PDFs. Templates are stored in the `documentation/Temp` directory and are loaded automatically when the application starts.

#### Testing Templates

To test a template against an invoice PDF:

1. Make sure the Docker containers are running:

   ```bash
   docker-compose up -d
   ```

2. Use the template testing scripts in the `scripts/template_tests` directory:

   ```bash
   # Copy your template to the container
   docker cp documentation/Temp/your_template.json expense_logger_api:/tmp/template.json

   # Run the test script
   docker exec expense_logger_api python /tmp/test_script.py
   ```

3. Alternatively, you can use the `docker_test_bh_template.py` script as a reference to create your own testing script.

### Database Changes

#### Automatic Migrations

The application uses SQLAlchemy models for database schema management:

1. The backend will automatically apply model changes on restart
2. This works for both SQLite and PostgreSQL databases

#### Manual Migrations

For major schema changes or data migrations:

1. Create a new SQL migration file in the `database/migrations` directory following the naming convention `NNN_descriptive_name.sql`
2. Migration files should include both PostgreSQL and SQLite compatible SQL
3. After adding a new migration file, restart the application:

```bash
docker-compose down
docker-compose up -d
```

#### Switching Database Types

If you want to switch between SQLite and PostgreSQL:

1. Back up your data (see Backup and Restore section)
2. Access the setup page at http://localhost:3000/setup
3. Choose the new database type and configure it
4. Import your data from the backup

See the `database/migrations/README.md` file for more information on creating migration files.

## Troubleshooting

### Common Issues

1. **Database Connection Issues**:

   For PostgreSQL:

   ```bash
   # Check if PostgreSQL is running
   docker-compose ps

   # View PostgreSQL logs
   docker-compose logs postgres_db
   ```

   For SQLite:

   ```bash
   # Check if the SQLite database file exists
   docker-compose exec backend ls -la /app/database/expense_logger.db

   # Check permissions on the database file
   docker-compose exec backend ls -la /app/database
   ```

2. **API Connection Issues**:

   ```bash
   # Verify the backend is running
   docker-compose ps backend

   # Check backend logs
   docker-compose logs backend
   ```

3. **Frontend Not Loading**:

   ```bash
   # Check frontend logs
   docker-compose logs frontend

   # Verify the API_URL is correct in .env
   ```

4. **Authentication Issues**:

   ```bash
   # Check if refresh tokens are being created
   docker-compose exec backend python -c "from features.auth.models import RefreshToken; from core.database import SessionLocal; print(SessionLocal().query(RefreshToken).all())"

   # Reset your password through the password reset feature
   # or create a new admin user through the setup wizard
   ```

5. **Workspace Issues**:

   ```bash
   # Check if workspaces are being created
   docker-compose exec backend python -c "from features.workspaces.models import Workspace; from core.database import SessionLocal; print(SessionLocal().query(Workspace).all())"
   ```

6. **Reset Everything**:

   ```bash
   # Stop all containers and remove volumes
   docker-compose down -v

   # Rebuild and start
   docker-compose up -d --build
   ```

## Backup and Restore

### Backup the Database

For PostgreSQL:

```bash
docker-compose exec postgres_db pg_dump -U postgres expense_logger > backup.sql
```

For SQLite:

```bash
# Copy the SQLite database file from the container
docker cp expense_logger_api:/app/database/expense_logger.db ./backup_expense_logger.db
```

### Backup Uploaded Files

```bash
# Create a backup of all uploaded files
docker cp expense_logger_api:/app/database/uploads ./uploads_backup
```

### Restore the Database

For PostgreSQL:

```bash
# Stop services
docker-compose down

# Start just the database
docker-compose up -d postgres_db

# Wait for it to initialize
sleep 10

# Restore from backup
docker-compose exec -T postgres_db psql -U postgres expense_logger < backup.sql

# Start all services
docker-compose up -d
```

For SQLite:

```bash
# Stop services
docker-compose down

# Start the backend service
docker-compose up -d backend

# Copy the backup file to the container
docker cp ./backup_expense_logger.db expense_logger_api:/app/database/expense_logger.db

# Restart the backend to ensure it uses the restored database
docker-compose restart backend
```

### Restore Uploaded Files

```bash
# Restore uploaded files from backup
docker cp ./uploads_backup/. expense_logger_api:/app/database/uploads/
```

## Production Deployment Notes

For production deployment:

1. Change all default passwords and secrets in the `.env` file
2. Set `NODE_ENV=production` for the frontend service
3. Configure proper SSL/TLS for secure connections
4. Set up a proper backup strategy
5. Consider using Docker Swarm or Kubernetes for container orchestration

## Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Docker Documentation](https://docs.docker.com/)
